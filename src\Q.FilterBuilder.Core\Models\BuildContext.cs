using System.Collections.Generic;
using Q.FilterBuilder.Core.Providers;

namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Enumeration of supported ORM adapters.
/// </summary>
public enum AdapterType
{
    /// <summary>
    /// Entity Framework Core adapter.
    /// </summary>
    EfCore,

    /// <summary>
    /// Dapper micro-ORM adapter.
    /// </summary>
    Dapper,

    /// <summary>
    /// ADO.NET adapter.
    /// </summary>
    AdoNet,

    /// <summary>
    /// Dynamic LINQ adapter.
    /// </summary>
    Linq,

    /// <summary>
    /// Entity Framework 6 adapter.
    /// </summary>
    Ef6
}

/// <summary>
/// Context object that maintains state during query building with adapter awareness.
/// </summary>
public class BuildContext
{
    /// <summary>
    /// Gets the list of parameter values collected during query building.
    /// </summary>
    public List<object> Parameters { get; } = new();

    /// <summary>
    /// Gets or sets the current parameter index for generating parameter names.
    /// </summary>
    public int ParameterIndex { get; set; } = 0;

    /// <summary>
    /// Gets the adapter type for this build context.
    /// </summary>
    public AdapterType AdapterType { get; private set; }

    /// <summary>
    /// Gets the query format provider for database-specific formatting.
    /// </summary>
    public IQueryFormatProvider QueryFormatProvider { get; private set; }

    /// <summary>
    /// Gets additional metadata for the build context.
    /// </summary>
    public Dictionary<string, object?> Metadata { get; } = new();

    /// <summary>
    /// Initializes a new instance of the BuildContext class.
    /// </summary>
    /// <param name="adapterType">The adapter type for this context</param>
    /// <param name="queryFormatProvider">The query format provider</param>
    public BuildContext(AdapterType adapterType, IQueryFormatProvider queryFormatProvider)
    {
        AdapterType = adapterType;
        QueryFormatProvider = queryFormatProvider;
    }

    /// <summary>
    /// Creates a parameter name for the current parameter index.
    /// </summary>
    /// <returns>A formatted parameter name</returns>
    public string CreateParameterName()
    {
        return QueryFormatProvider.FormatParameterName(ParameterIndex);
    }

    /// <summary>
    /// Adds a parameter value and increments the parameter index.
    /// </summary>
    /// <param name="value">The parameter value to add</param>
    /// <returns>The parameter name that was created</returns>
    public string AddParameter(object? value)
    {
        var parameterName = CreateParameterName();
        Parameters.Add(value ?? System.DBNull.Value);
        ParameterIndex++;
        return parameterName;
    }

    /// <summary>
    /// Adds multiple parameter values and increments the parameter index accordingly.
    /// </summary>
    /// <param name="values">The parameter values to add</param>
    /// <returns>An array of parameter names that were created</returns>
    public string[] AddParameters(params object?[] values)
    {
        var parameterNames = new string[values.Length];
        for (int i = 0; i < values.Length; i++)
        {
            parameterNames[i] = AddParameter(values[i]);
        }
        return parameterNames;
    }

    /// <summary>
    /// Determines if the current adapter supports a specific feature.
    /// </summary>
    /// <param name="feature">The feature to check</param>
    /// <returns>True if the adapter supports the feature, false otherwise</returns>
    public bool SupportsFeature(string feature)
    {
        return feature.ToLowerInvariant() switch
        {
            "expressions" => AdapterType == AdapterType.Linq,
            "raw_sql" => AdapterType is AdapterType.EfCore or AdapterType.Dapper or AdapterType.AdoNet or AdapterType.Ef6,
            "dynamic_parameters" => AdapterType == AdapterType.Dapper,
            "db_parameters" => AdapterType == AdapterType.AdoNet,
            "formatted_queries" => AdapterType is AdapterType.EfCore or AdapterType.Ef6,
            _ => false
        };
    }
}
