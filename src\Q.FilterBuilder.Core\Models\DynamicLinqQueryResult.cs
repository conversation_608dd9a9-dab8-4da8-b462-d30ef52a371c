using System.Collections.Generic;

namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Query result specifically formatted for Dynamic LINQ.
/// Contains the WHERE clause, LINQ expression string, and parameter values for Dynamic LINQ queries.
/// </summary>
public class DynamicLinqQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the Dynamic LINQ expression string with parameter placeholders.
    /// Example: "Name.Contains(@0) && Age > @1"
    /// This uses Dynamic LINQ parameter syntax (@0, @1, etc.) instead of SQL parameters.
    /// </summary>
    public string LinqExpression { get; }

    /// <summary>
    /// Gets the parameter values in the correct order for the LINQ expression.
    /// These values correspond to the @0, @1, etc. placeholders in LinqExpression.
    /// </summary>
    public object[] Parameters { get; }

    /// <summary>
    /// Gets the parameters formatted as a dictionary for some Dynamic LINQ scenarios.
    /// Keys are parameter names (e.g., "param0", "param1") and values are the parameter values.
    /// </summary>
    public Dictionary<string, object?> ParameterDictionary { get; }

    /// <summary>
    /// Creates a new Dynamic LINQ query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="linqExpression">The Dynamic LINQ expression string</param>
    /// <param name="parameters">The parameter values</param>
    /// <param name="parameterDictionary">The parameter dictionary (optional)</param>
    public DynamicLinqQueryResult(string whereClause, string linqExpression, object[] parameters, Dictionary<string, object?>? parameterDictionary = null)
    {
        WhereClause = whereClause ?? throw new System.ArgumentNullException(nameof(whereClause));
        LinqExpression = linqExpression ?? throw new System.ArgumentNullException(nameof(linqExpression));
        Parameters = parameters ?? throw new System.ArgumentNullException(nameof(parameters));
        ParameterDictionary = parameterDictionary ?? CreateParameterDictionary(parameters);
    }

    /// <summary>
    /// Creates a parameter dictionary from parameter values.
    /// </summary>
    /// <param name="parameters">The parameter values</param>
    /// <returns>A dictionary with parameter names as keys</returns>
    private static Dictionary<string, object?> CreateParameterDictionary(object[] parameters)
    {
        var dict = new Dictionary<string, object?>();
        for (var i = 0; i < parameters.Length; i++)
        {
            dict[$"param{i}"] = parameters[i];
        }
        return dict;
    }
}
