﻿using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core;

/// <summary>
/// Defines the interface for filter builders that generate database queries.
/// This is the main interface that should be injected into controllers and services.
/// </summary>
public interface IFilterBuilder
{
    /// <summary>
    /// Builds a WHERE clause based on the provided FilterGroup.
    /// </summary>
    /// <param name="group">The FilterGroup to build the WHERE clause from.</param>
    /// <returns>A tuple containing the parsed query string and an array of parameters.</returns>
    (string parsedQuery, object[] parameters) Build(FilterGroup group);

    /// <summary>
    /// Builds a query result specifically formatted for Dapper.
    /// </summary>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DapperQueryResult with query and parameter dictionary.</returns>
    DapperQueryResult BuildForDapper(FilterGroup group);

    /// <summary>
    /// Builds a query result specifically formatted for Entity Framework Core.
    /// </summary>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An EfCoreQueryResult with formatted query and parameters.</returns>
    EfCoreQueryResult BuildForEfCore(FilterGroup group);

    /// <summary>
    /// Builds a query result specifically formatted for Entity Framework 6.
    /// </summary>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An EfQueryResult with formatted query and parameters.</returns>
    EfQueryResult BuildForEf(FilterGroup group);

    /// <summary>
    /// Builds a query result specifically formatted for ADO.NET.
    /// </summary>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An AdoNetQueryResult with database parameters.</returns>
    AdoNetQueryResult BuildForAdoNet(FilterGroup group);

    /// <summary>
    /// Builds a query result specifically formatted for Dynamic LINQ.
    /// </summary>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DynamicLinqQueryResult with LINQ expression and parameters.</returns>
    DynamicLinqQueryResult BuildForDynamicLinq(FilterGroup group);
}