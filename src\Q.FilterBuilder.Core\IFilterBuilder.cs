﻿using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core;

/// <summary>
/// Defines the interface for filter builders that generate database queries.
/// This is the main interface that should be injected into controllers and services.
/// </summary>
public interface IFilterBuilder
{
    /// <summary>
    /// Builds a WHERE clause based on the provided FilterGroup.
    /// </summary>
    /// <param name="group">The FilterGroup to build the WHERE clause from.</param>
    /// <returns>A tuple containing the parsed query string and an array of parameters.</returns>
    (string parsedQuery, object[] parameters) Build(FilterGroup group);
}

/// <summary>
/// Generic interface for adapter-specific filter builders that generate typed query results.
/// </summary>
/// <typeparam name="TResult">The type of result returned by this adapter</typeparam>
public interface IFilterBuilder<TResult> where TResult : class
{
    /// <summary>
    /// Builds an adapter-specific query result based on the provided FilterGroup.
    /// </summary>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An adapter-specific result containing query and parameters in the appropriate format.</returns>
    TResult Build(FilterGroup group);
}

/// <summary>
/// Factory interface for creating adapter-specific filter builders.
/// </summary>
public interface IFilterBuilderFactory
{
    /// <summary>
    /// Creates an EF Core filter builder.
    /// </summary>
    /// <returns>An EF Core filter builder instance.</returns>
    IFilterBuilder<EfCoreQueryResult> CreateEfCoreBuilder();

    /// <summary>
    /// Creates a Dapper filter builder.
    /// </summary>
    /// <returns>A Dapper filter builder instance.</returns>
    IFilterBuilder<DapperQueryResult> CreateDapperBuilder();

    /// <summary>
    /// Creates an ADO.NET filter builder.
    /// </summary>
    /// <returns>An ADO.NET filter builder instance.</returns>
    IFilterBuilder<AdoNetQueryResult> CreateAdoNetBuilder();

    /// <summary>
    /// Creates a Dynamic LINQ filter builder.
    /// </summary>
    /// <returns>A Dynamic LINQ filter builder instance.</returns>
    IFilterBuilder<LinqQueryResult> CreateLinqBuilder();

    /// <summary>
    /// Creates an EF6 filter builder.
    /// </summary>
    /// <returns>An EF6 filter builder instance.</returns>
    IFilterBuilder<Ef6QueryResult> CreateEf6Builder();
}