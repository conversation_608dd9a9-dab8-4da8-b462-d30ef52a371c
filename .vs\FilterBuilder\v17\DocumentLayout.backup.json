{"Version": 1, "WorkspaceRootPath": "C:\\git-repos\\DynamicWhereBuilder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\extensions\\efcoreextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\extensions\\efcoreextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\extensions\\dapperextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\extensions\\dapperextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\extensions\\adonetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\extensions\\adonetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\ifilterbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\ifilterbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.sqlserver\\ruletransformers\\beginswithruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|solutionrelative:src\\q.filterbuilder.sqlserver\\ruletransformers\\beginswithruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.postgresql\\ruletransformers\\beginswithruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|solutionrelative:src\\q.filterbuilder.postgresql\\ruletransformers\\beginswithruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.postgresql\\ruletransformers\\endswithruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|solutionrelative:src\\q.filterbuilder.postgresql\\ruletransformers\\endswithruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.sqlserver\\sqlserverformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|solutionrelative:src\\q.filterbuilder.sqlserver\\sqlserverformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.postgresql\\postgresqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|solutionrelative:src\\q.filterbuilder.postgresql\\postgresqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{60C60E09-2D48-404F-BD9B-FB2DA98597D9}|src\\Q.FilterBuilder.MySql\\Q.FilterBuilder.MySql.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.mysql\\mysqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{60C60E09-2D48-404F-BD9B-FB2DA98597D9}|src\\Q.FilterBuilder.MySql\\Q.FilterBuilder.MySql.csproj|solutionrelative:src\\q.filterbuilder.mysql\\mysqlformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.sqlserver\\ruletransformers\\betweenruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|solutionrelative:src\\q.filterbuilder.sqlserver\\ruletransformers\\betweenruletransformer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.core\\filterbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A5607035-467C-4633-B2A4-2D2A1E90DB1E}|src\\Q.FilterBuilder.Core\\Q.FilterBuilder.Core.csproj|solutionrelative:src\\q.filterbuilder.core\\filterbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.sqlserver\\extensions\\sqlserverormextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|solutionrelative:src\\q.filterbuilder.sqlserver\\extensions\\sqlserverormextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\services\\ormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\services\\ormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\appsettings.test.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.postgresql\\extensions\\postgresqlormextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{48A91BC9-556C-4383-B5BE-466A0D0F0D6C}|src\\Q.FilterBuilder.PostgreSql\\Q.FilterBuilder.PostgreSql.csproj|solutionrelative:src\\q.filterbuilder.postgresql\\extensions\\postgresqlormextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\services\\iormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\services\\iormexecutionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\sqlserverproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\sqlserverproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestwebapplicationfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestwebapplicationfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\providerstrategyfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\providerstrategyfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\postgresqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\postgresqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\baseproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\baseproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\teststartup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\teststartup.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\database\\testdataseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\database\\testdataseeder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\integrationtestbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\databasecontainerfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\databasecontainerfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\mysqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\mysqlproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\developer_guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\developer_guide.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\readme.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}|"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\iproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\infrastructure\\providers\\iproviderstrategy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\getting_started.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\getting_started.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\database\\testdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\database\\testdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\configuration\\testconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\configuration\\testconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\configuration\\databaseprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\configuration\\databaseprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.integrationtests\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E5F6A7B8-C9D0-1234-EFAB-************}|test\\Q.FilterBuilder.IntegrationTests\\Q.FilterBuilder.IntegrationTests.csproj|solutionrelative:test\\q.filterbuilder.integrationtests\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 454, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "IFilterBuilder.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\IFilterBuilder.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\IFilterBuilder.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\IFilterBuilder.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\IFilterBuilder.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T13:00:17.243Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "EfCoreExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\EfCoreExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\Extensions\\EfCoreExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\EfCoreExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\Extensions\\EfCoreExtensions.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAUwBoAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T13:00:39.976Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DapperExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\Extensions\\DapperExtensions.cs", "ViewState": "AgIAABoAAAAAAAAAAAAiwBsAAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T12:57:59.018Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AdoNetExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\Extensions\\AdoNetExtensions.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAiwEUAAABUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T12:57:30.772Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "BeginsWithRuleTransformer.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BeginsWithRuleTransformer.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BeginsWithRuleTransformer.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BeginsWithRuleTransformer.cs", "RelativeToolTip": "src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BeginsWithRuleTransformer.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAqwAkAAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T09:59:17.594Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "EndsWithRuleTransformer.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\EndsWithRuleTransformer.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\EndsWithRuleTransformer.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\EndsWithRuleTransformer.cs", "RelativeToolTip": "src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\EndsWithRuleTransformer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T09:58:53.225Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "BeginsWithRuleTransformer.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\BeginsWithRuleTransformer.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\BeginsWithRuleTransformer.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\BeginsWithRuleTransformer.cs", "RelativeToolTip": "src\\Q.FilterBuilder.PostgreSql\\RuleTransformers\\BeginsWithRuleTransformer.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAkAAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T09:44:50.428Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "PostgreSqlFormatProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "RelativeToolTip": "src\\Q.FilterBuilder.PostgreSql\\PostgreSqlFormatProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAcAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T04:02:30.636Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "SqlServerFormatProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "RelativeToolTip": "src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "ViewState": "AgIAABkAAAAAAAAAAAAIwB0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T04:02:12.994Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "MySqlFormatProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "RelativeToolTip": "src\\Q.FilterBuilder.MySql\\MySqlFormatProvider.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABMAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T04:05:27.522Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "BetweenRuleTransformer.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BetweenRuleTransformer.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BetweenRuleTransformer.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BetweenRuleTransformer.cs", "RelativeToolTip": "src\\Q.FilterBuilder.SqlServer\\RuleTransformers\\BetweenRuleTransformer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T03:41:25.723Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "FilterBuilder.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\FilterBuilder.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.Core\\FilterBuilder.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.Core\\FilterBuilder.cs", "RelativeToolTip": "src\\Q.FilterBuilder.Core\\FilterBuilder.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAwwGcAAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T03:40:39.882Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "IOrmExecutionService.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\IOrmExecutionService.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Services\\IOrmExecutionService.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\IOrmExecutionService.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Services\\IOrmExecutionService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T11:08:44.28Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "OrmExecutionService.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Services\\OrmExecutionService.cs", "ViewState": "AgIAAEwAAAAAAAAAAAAjwF0AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T09:35:42.47Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "SqlServerOrmExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\Extensions\\SqlServerOrmExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.SqlServer\\Extensions\\SqlServerOrmExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\Extensions\\SqlServerOrmExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.SqlServer\\Extensions\\SqlServerOrmExtensions.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAIwDwAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T09:34:30.749Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "BaseProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\BaseProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\BaseProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\BaseProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\BaseProviderStrategy.cs", "ViewState": "AgIAADUAAAAAAAAAAAAIwDgAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T04:43:59.439Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "TestStartup.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\TestStartup.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAQwBcAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:31:15.551Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "SqlServerProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\SqlServerProviderStrategy.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAowB8AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:36:30.687Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "TestDataSeeder.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDataSeeder.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABUAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:53:39.005Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "appsettings.test.json", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\appsettings.test.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-10T05:12:33.089Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "PostgreSqlOrmExtensions.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\Extensions\\PostgreSqlOrmExtensions.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.PostgreSql\\Extensions\\PostgreSqlOrmExtensions.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.PostgreSql\\Extensions\\PostgreSqlOrmExtensions.cs", "RelativeToolTip": "src\\Q.FilterBuilder.PostgreSql\\Extensions\\PostgreSqlOrmExtensions.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAcwBgAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T11:14:21.762Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "ProviderStrategyFactory.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\ProviderStrategyFactory.cs", "ViewState": "AgIAACkAAAAAAAAAAAA9wDUAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:38:05.498Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "PostgreSqlProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\PostgreSqlProviderStrategy.cs", "ViewState": "AgIAABIAAAAAAAAAAAAowCQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:37:29.624Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "IntegrationTestWebApplicationFactory.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestWebApplicationFactory.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowB8AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:34:36.407Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "DEVELOPER_GUIDE.md", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\DEVELOPER_GUIDE.md", "ViewState": "AgIAALIAAAAAAAAAAAAlwMIAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-10T05:01:54.857Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "MySqlProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\MySqlProviderStrategy.cs", "ViewState": "AgIAABEAAAAAAAAAAIAywBgAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T05:23:21.431Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "README.md", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\README.md", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\README.md", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\README.md", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\README.md", "ViewState": "AgIAALMAAAAAAAAAAAAlwMcAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-10T05:01:54.683Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "IntegrationTestBase.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\IntegrationTestBase.cs", "ViewState": "AgIAAAwAAAAAAAAAAAA5wB4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:03:17.191Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "DatabaseContainerFixture.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\DatabaseContainerFixture.cs", "ViewState": "AgIAABsAAAAAAAAAAAAWwCkAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:33:08.523Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "IProviderStrategy.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Infrastructure\\Providers\\IProviderStrategy.cs", "ViewState": "AgIAACAAAAAAAAAAAAAjwC4AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:36:09.568Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "GETTING_STARTED.md", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\GETTING_STARTED.md", "ViewState": "AgIAANgAAAAAAAAAAAAAAOsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-06-11T02:41:49.689Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "TestDbContext.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Database\\TestDbContext.cs", "ViewState": "AgIAAKUAAAAAAAAAAAAmwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:35:54.136Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "DatabaseProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\DatabaseProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:30:19.96Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "TestConfiguration.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Configuration\\TestConfiguration.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAcwAsAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:29:21.304Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "Program.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "RelativeToolTip": "test\\Q.FilterBuilder.IntegrationTests\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-10T04:28:39.648Z"}]}]}]}