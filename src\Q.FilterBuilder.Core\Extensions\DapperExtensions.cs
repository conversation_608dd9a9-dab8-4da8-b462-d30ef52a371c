using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support Dapper integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class DapperExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Dapper.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DapperQueryResult with query and parameter dictionary.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForDapper(filterGroup);
    /// var users = connection.Query&lt;User&gt;($"SELECT * FROM Users WHERE {result.WhereClause}", result.Parameters);
    /// </code>
    /// </example>
    public static DapperQueryResult BuildForDapper(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var dapperParameters = filterBuilder.QueryFormatProvider.ToDapperParameters(parameters);
        return new DapperQueryResult(query, dapperParameters);
    }
}
