using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support Entity Framework Core integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class EfCoreExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Entity Framework Core.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An EfCoreQueryResult with formatted query and parameters.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForEfCore(filterGroup);
    /// var users = context.Users.FromSqlRaw($"SELECT * FROM Users WHERE {result.FormattedQuery}", result.Parameters);
    /// </code>
    /// </example>
    public static EfCoreQueryResult BuildForEfCore(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var formattedQuery = filterBuilder.QueryFormatProvider.ToEfCoreFormat(query);
        return new EfCoreQueryResult(query, formattedQuery, parameters);
    }
}
