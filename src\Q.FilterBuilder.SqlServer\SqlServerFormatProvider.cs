using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Text.RegularExpressions;
using Q.FilterBuilder.Core.Providers;

namespace Q.FilterBuilder.SqlServer;

/// <summary>
/// SQL Server query syntax provider implementation.
/// </summary>
public class SqlServerFormatProvider : IQueryFormatProvider
{
    /// <inheritdoc />
    public string ParameterPrefix => "@";

    /// <inheritdoc />
    public string AndOperator => "AND";

    /// <inheritdoc />
    public string OrOperator => "OR";

    /// <inheritdoc />
    public string FormatFieldName(string fieldName)
    {
        return $"[{fieldName}]";
    }

    /// <inheritdoc />
    public string FormatParameterName(int parameterIndex)
    {
        return $"{ParameterPrefix}p{parameterIndex}";
    }

    /// <inheritdoc />
    public string ToEfCoreFormat(string whereClause)
    {
        var efQuery = whereClause;
        // Use regex to find all @p0, @p1, etc.
        var matches = Regex.Matches(efQuery, @"@p\d+");

        // Replace each match with {index} format
        foreach (Match match in matches)
        {
            var index = int.Parse(match.Value.Substring(2));
            efQuery = efQuery.Replace(match.Value, $"{{{index}}}");
        }

        return efQuery;
    }

    /// <inheritdoc />
    public Dictionary<string, object?> ToDapperParameters(object[] parameterValues)
    {
        var paramDict = new Dictionary<string, object?>();
        for (var i = 0; i < parameterValues.Length; i++)
        {
            paramDict[FormatParameterName(i)] = parameterValues[i];
        }
        return paramDict;
    }

    /// <inheritdoc />
    public IDbDataParameter[] ToAdoNetParameters(object[] parameterValues)
    {
        // For now, return an empty array - this will be implemented by specific database providers
        // that have access to their specific parameter types
        return new IDbDataParameter[0];
    }

    /// <inheritdoc />
    public string ToLinqExpression(string whereClause, object[] parameterValues)
    {
        // For SQL Server, convert SQL syntax to LINQ syntax
        // This is a simplified implementation - a full implementation would need more sophisticated parsing
        var linqExpression = whereClause;

        // Replace SQL operators with LINQ equivalents
        linqExpression = linqExpression.Replace(" = ", " == ");
        linqExpression = linqExpression.Replace(" <> ", " != ");
        linqExpression = linqExpression.Replace(" AND ", " && ");
        linqExpression = linqExpression.Replace(" OR ", " || ");

        // Replace field brackets with property access
        linqExpression = Regex.Replace(linqExpression, @"\[(\w+)\]", "$1");

        // Replace parameters with actual values (simplified)
        for (var i = 0; i < parameterValues.Length; i++)
        {
            var paramName = FormatParameterName(i);
            var value = parameterValues[i];
            var valueString = value switch
            {
                string s => $"\"{s}\"",
                null => "null",
                _ => value.ToString()
            };
            linqExpression = linqExpression.Replace(paramName, valueString);
        }

        return linqExpression;
    }
}
