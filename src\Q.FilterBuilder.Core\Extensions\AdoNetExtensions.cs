using System.Data;
using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support ADO.NET integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class AdoNetExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for ADO.NET.
    /// This method provides both provider-specific parameters (if available) and raw parameter values.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An AdoNetQueryResult with database parameters and raw values.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForAdoNet(filterGroup);
    /// using var command = connection.CreateCommand();
    /// command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";
    /// 
    /// // Option 1: Use provider-specific parameters if available
    /// foreach (var param in result.Parameters)
    /// {
    ///     command.Parameters.Add(param);
    /// }
    /// 
    /// // Option 2: Create your own parameters from raw values
    /// for (int i = 0; i &lt; result.ParameterValues.Length; i++)
    /// {
    ///     var param = command.CreateParameter();
    ///     param.ParameterName = $"@p{i}";
    ///     param.Value = result.ParameterValues[i] ?? DBNull.Value;
    ///     command.Parameters.Add(param);
    /// }
    /// </code>
    /// </example>
    public static AdoNetQueryResult BuildForAdoNet(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        // Default implementation returns empty parameters array - use BuildForAdoNetWithConnection for automatic parameter creation
        return new AdoNetQueryResult(query, parameters);
    }

    /// <summary>
    /// Builds a query result for ADO.NET with custom parameter creation using the provided connection.
    /// This method creates database-specific parameters using the connection's CreateParameter method.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <param name="connection">The database connection to create parameters for.</param>
    /// <returns>An AdoNetQueryResult with connection-specific database parameters.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForAdoNetWithConnection(filterGroup, connection);
    /// using var command = connection.CreateCommand();
    /// command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";
    /// foreach (var param in result.Parameters)
    /// {
    ///     command.Parameters.Add(param);
    /// }
    /// </code>
    /// </example>
    public static AdoNetQueryResult BuildForAdoNetWithConnection(this IFilterBuilder filterBuilder, FilterGroup group, IDbConnection connection)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var dbParameters = CreateParametersWithConnection(parameters, filterBuilder.QueryFormatProvider, connection);
        return new AdoNetQueryResult(query, dbParameters, parameters);
    }

    /// <summary>
    /// Creates database parameters using the connection's CreateParameter method.
    /// This ensures the parameters are compatible with the specific database provider.
    /// </summary>
    /// <param name="parameterValues">The parameter values.</param>
    /// <param name="provider">The query format provider.</param>
    /// <param name="connection">The database connection.</param>
    /// <returns>An array of database-specific parameters.</returns>
    private static IDbDataParameter[] CreateParametersWithConnection(object[] parameterValues, Providers.IQueryFormatProvider provider, IDbConnection connection)
    {
        var dbParameters = new IDbDataParameter[parameterValues.Length];
        
        using var tempCommand = connection.CreateCommand();
        for (var i = 0; i < parameterValues.Length; i++)
        {
            var parameter = tempCommand.CreateParameter();
            parameter.ParameterName = provider.FormatParameterName(i);
            parameter.Value = parameterValues[i] ?? System.DBNull.Value;
            dbParameters[i] = parameter;
        }
        
        return dbParameters;
    }
}
