using System;
using System.Data;
using Microsoft.Data.SqlClient;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.SqlServer.Extensions;

/// <summary>
/// SQL Server specific extension methods for IFilterBuilder.
/// These extensions provide optimized implementations for SQL Server database operations.
/// </summary>
public static class SqlServerFilterBuilderExtensions
{
    /// <summary>
    /// Builds a query result specifically optimized for SQL Server ADO.NET operations.
    /// This overrides the default implementation to provide actual SqlParameter objects.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An AdoNetQueryResult with SQL Server specific database parameters.</returns>
    public static AdoNetQueryResult BuildForSqlServerAdoNet(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var sqlParameters = CreateSqlParameters(parameters, filterBuilder.QueryFormatProvider);
        return new AdoNetQueryResult(query, sqlParameters);
    }

    /// <summary>
    /// Builds a query result specifically optimized for SQL Server with connection-specific parameters.
    /// This method creates SqlParameter objects that are ready to use with SqlCommand.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <param name="connection">The SQL Server connection to create parameters for.</param>
    /// <returns>An AdoNetQueryResult with connection-specific SQL parameters.</returns>
    public static AdoNetQueryResult BuildForSqlServerWithConnection(this IFilterBuilder filterBuilder, FilterGroup group, SqlConnection connection)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var sqlParameters = CreateSqlParametersWithConnection(parameters, filterBuilder.QueryFormatProvider, connection);
        return new AdoNetQueryResult(query, sqlParameters);
    }

    /// <summary>
    /// Creates SQL Server specific SqlParameter objects from parameter values.
    /// </summary>
    /// <param name="parameterValues">The parameter values.</param>
    /// <param name="provider">The query format provider.</param>
    /// <returns>An array of SqlParameter objects.</returns>
    private static IDbDataParameter[] CreateSqlParameters(object[] parameterValues, Core.Providers.IQueryFormatProvider provider)
    {
        var sqlParameters = new IDbDataParameter[parameterValues.Length];
        for (var i = 0; i < parameterValues.Length; i++)
        {
            var parameter = new SqlParameter
            {
                ParameterName = provider.FormatParameterName(i),
                Value = parameterValues[i] ?? DBNull.Value
            };
            
            // Set SQL Server specific type information based on value type
            SetSqlServerTypeInfo(parameter, parameterValues[i]);
            
            sqlParameters[i] = parameter;
        }
        return sqlParameters;
    }

    /// <summary>
    /// Creates SQL Server specific SqlParameter objects using a connection for optimal type mapping.
    /// </summary>
    /// <param name="parameterValues">The parameter values.</param>
    /// <param name="provider">The query format provider.</param>
    /// <param name="connection">The SQL connection for creating parameters.</param>
    /// <returns>An array of SqlParameter objects.</returns>
    private static IDbDataParameter[] CreateSqlParametersWithConnection(object[] parameterValues, Core.Providers.IQueryFormatProvider provider, SqlConnection connection)
    {
        var sqlParameters = new IDbDataParameter[parameterValues.Length];
        using var command = connection.CreateCommand();
        
        for (var i = 0; i < parameterValues.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = provider.FormatParameterName(i);
            parameter.Value = parameterValues[i] ?? DBNull.Value;
            
            // Let SQL Server infer the best type
            if (parameter is SqlParameter sqlParam && parameterValues[i] != null)
            {
                SetSqlServerTypeInfo(sqlParam, parameterValues[i]);
            }
            
            sqlParameters[i] = parameter;
        }
        return sqlParameters;
    }

    /// <summary>
    /// Sets SQL Server specific type information for optimal performance.
    /// </summary>
    /// <param name="parameter">The SQL parameter to configure.</param>
    /// <param name="value">The parameter value.</param>
    private static void SetSqlServerTypeInfo(SqlParameter parameter, object? value)
    {
        if (value == null) return;

        switch (value)
        {
            case string s:
                parameter.SqlDbType = SqlDbType.NVarChar;
                parameter.Size = s.Length > 4000 ? -1 : Math.Max(s.Length, 50); // Use MAX for large strings
                break;
            case int:
                parameter.SqlDbType = SqlDbType.Int;
                break;
            case long:
                parameter.SqlDbType = SqlDbType.BigInt;
                break;
            case decimal:
                parameter.SqlDbType = SqlDbType.Decimal;
                break;
            case double:
                parameter.SqlDbType = SqlDbType.Float;
                break;
            case float:
                parameter.SqlDbType = SqlDbType.Real;
                break;
            case bool:
                parameter.SqlDbType = SqlDbType.Bit;
                break;
            case DateTime:
                parameter.SqlDbType = SqlDbType.DateTime2;
                break;
            case DateTimeOffset:
                parameter.SqlDbType = SqlDbType.DateTimeOffset;
                break;
            case Guid:
                parameter.SqlDbType = SqlDbType.UniqueIdentifier;
                break;
            case byte[]:
                parameter.SqlDbType = SqlDbType.VarBinary;
                break;
            default:
                // Let SQL Server infer the type for other types
                break;
        }
    }
}
