using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.SqlServer;
using System.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Dapper;

namespace Q.FilterBuilder.Examples;

/// <summary>
/// Example demonstrating the new simplified adapter-specific methods.
/// </summary>
public class AdapterUsageExample
{
    private readonly IFilterBuilder _filterBuilder;

    public AdapterUsageExample()
    {
        // Simple setup - just inject IFilterBuilder
        _filterBuilder = new FilterBuilder(new SqlServerFormatProvider());
    }

    public void DemonstrateUsage()
    {
        // Create a filter group
        var filters = new FilterGroup("AND");
        filters.Rules.Add(new FilterRule("Name", "contains", "John"));
        filters.Rules.Add(new FilterRule("Age", "greater", 25));
        filters.Rules.Add(new FilterRule("IsActive", "equal", true));

        // 1. For Dapper - get query and parameter dictionary
        var dapperResult = _filterBuilder.BuildForDapper(filters);
        // Use with Dapper: connection.Query<User>($"SELECT * FROM Users WHERE {dapperResult.WhereClause}", dapperResult.Parameters);

        // 2. For EF Core - get formatted query with {0}, {1} placeholders
        var efCoreResult = _filterBuilder.BuildForEfCore(filters);
        // Use with EF Core: context.Users.FromSqlRaw($"SELECT * FROM Users WHERE {efCoreResult.FormattedQuery}", efCoreResult.Parameters);

        // 3. For EF6 - same format as EF Core
        var efResult = _filterBuilder.BuildForEf(filters);
        // Use with EF6: context.Users.SqlQuery($"SELECT * FROM Users WHERE {efResult.FormattedQuery}", efResult.Parameters);

        // 4. For ADO.NET - get database parameters (provider-specific implementation needed)
        var adoNetResult = _filterBuilder.BuildForAdoNet(filters);
        // Use with ADO.NET: command.CommandText = $"SELECT * FROM Users WHERE {adoNetResult.WhereClause}"; 
        // foreach(var param in adoNetResult.Parameters) command.Parameters.Add(param);

        // 5. For Dynamic LINQ - get LINQ expression string
        var linqResult = _filterBuilder.BuildForDynamicLinq(filters);
        // Use with Dynamic LINQ: users.Where(linqResult.LinqExpression);

        // 6. Traditional way still works
        var (query, parameters) = _filterBuilder.Build(filters);
        // Use however you want: query contains WHERE clause, parameters contains values
    }

    // Example with Entity Framework Core
    public async Task<List<User>> GetUsersWithEfCore(DbContext context, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForEfCore(filters);
        var sql = $"SELECT * FROM Users WHERE {result.FormattedQuery}";
        return await context.Set<User>().FromSqlRaw(sql, result.Parameters).ToListAsync();
    }

    // Example with Dapper
    public async Task<List<User>> GetUsersWithDapper(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForDapper(filters);
        var sql = $"SELECT * FROM Users WHERE {result.WhereClause}";
        return (await connection.QueryAsync<User>(sql, result.Parameters)).ToList();
    }

    // Example with ADO.NET (when provider implements ToAdoNetParameters)
    public async Task<List<User>> GetUsersWithAdoNet(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForAdoNet(filters);
        using var command = connection.CreateCommand();
        command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";
        
        foreach (var param in result.Parameters)
        {
            command.Parameters.Add(param);
        }

        // Execute and map results...
        return new List<User>(); // Simplified for example
    }
}

public class User
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Age { get; set; }
    public bool IsActive { get; set; }
}
