using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Extensions; // Import all adapter extensions
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.SqlServer;
using Q.FilterBuilder.SqlServer.Extensions;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Dapper;

namespace Q.FilterBuilder.Examples;

/// <summary>
/// Example demonstrating the new organized, SOLID-compliant adapter-specific extension methods.
/// This approach follows SOLID principles with separate files for each adapter and result type.
/// Each adapter has its own extension file and result type for better organization and maintainability.
/// </summary>
public class AdapterUsageExample
{
    private readonly IFilterBuilder _filterBuilder;

    public AdapterUsageExample()
    {
        // Simple setup - just inject IFilterBuilder
        _filterBuilder = new FilterBuilder(new SqlServerFormatProvider());
    }

    public void DemonstrateUsage()
    {
        // Create a filter group
        var filters = new FilterGroup("AND");
        filters.Rules.Add(new FilterRule("Name", "contains", "John"));
        filters.Rules.Add(new FilterRule("Age", "greater", 25));
        filters.Rules.Add(new FilterRule("IsActive", "equal", true));

        // 1. Dapper (from DapperExtensions.cs) - parameter dictionary
        var dapperResult = _filterBuilder.BuildForDapper(filters);
        // Use: connection.Query<User>($"SELECT * FROM Users WHERE {dapperResult.WhereClause}", dapperResult.Parameters);

        // 2. EF Core (from EfCoreExtensions.cs) - formatted query with {0}, {1} placeholders
        var efCoreResult = _filterBuilder.BuildForEfCore(filters);
        // Use: context.Users.FromSqlRaw($"SELECT * FROM Users WHERE {efCoreResult.FormattedQuery}", efCoreResult.Parameters);

        // 3. EF6 (from EfExtensions.cs) - same format as EF Core
        var efResult = _filterBuilder.BuildForEf(filters);
        // Use: context.Users.SqlQuery($"SELECT * FROM Users WHERE {efResult.FormattedQuery}", efResult.Parameters);

        // 4. ADO.NET (from AdoNetExtensions.cs) - database parameters + raw values
        var adoNetResult = _filterBuilder.BuildForAdoNet(filters);
        // Use: command.CommandText = $"SELECT * FROM Users WHERE {adoNetResult.WhereClause}";
        // Option A: foreach(var param in adoNetResult.Parameters) command.Parameters.Add(param);
        // Option B: Create your own parameters from adoNetResult.ParameterValues

        // 5. ADO.NET with Connection (from AdoNetExtensions.cs) - connection-specific parameters
        using var connection = new SqlConnection("connection string");
        var adoNetConnResult = _filterBuilder.BuildForAdoNetWithConnection(filters, connection);
        // Use: foreach(var param in adoNetConnResult.Parameters) command.Parameters.Add(param);

        // 6. SQL Server ADO.NET (from SqlServerFilterBuilderExtensions.cs) - optimized SQL Server parameters
        var sqlServerResult = _filterBuilder.BuildForSqlServerAdoNet(filters);
        // Use: foreach(var param in sqlServerResult.Parameters) command.Parameters.Add(param);

        // 7. Dynamic LINQ (from DynamicLinqExtensions.cs) - LINQ expression with @0, @1 syntax
        var linqResult = _filterBuilder.BuildForDynamicLinq(filters);
        // Use: users.Where(linqResult.LinqExpression, linqResult.Parameters);
        // Or:  users.Where(linqResult.LinqExpression, linqResult.ParameterDictionary);

        // 8. Dynamic LINQ with Field Mapping (from DynamicLinqExtensions.cs)
        var fieldMapping = new Dictionary<string, string> { { "Name", "UserName" } };
        var mappedLinqResult = _filterBuilder.BuildForDynamicLinqWithMapping(filters, fieldMapping);

        // 9. Traditional way still works
        var (query, parameters) = _filterBuilder.Build(filters);
        // Use however you want: query contains WHERE clause, parameters contains values
    }

    // Example with Entity Framework Core
    public async Task<List<User>> GetUsersWithEfCore(DbContext context, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForEfCore(filters);
        var sql = $"SELECT * FROM Users WHERE {result.FormattedQuery}";
        return await context.Set<User>().FromSqlRaw(sql, result.Parameters).ToListAsync();
    }

    // Example with Dapper
    public async Task<List<User>> GetUsersWithDapper(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForDapper(filters);
        var sql = $"SELECT * FROM Users WHERE {result.WhereClause}";
        return (await connection.QueryAsync<User>(sql, result.Parameters)).ToList();
    }

    // Example with ADO.NET using default implementation
    public async Task<List<User>> GetUsersWithAdoNet(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForAdoNet(filters);
        using var command = connection.CreateCommand();
        command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";

        foreach (var param in result.Parameters)
        {
            command.Parameters.Add(param);
        }

        // Execute and map results...
        return new List<User>(); // Simplified for example
    }

    // Example with SQL Server optimized ADO.NET (provider-specific extension)
    public async Task<List<User>> GetUsersWithSqlServerAdoNet(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForSqlServerAdoNet(filters);
        using var command = connection.CreateCommand();
        command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";

        foreach (var param in result.Parameters)
        {
            command.Parameters.Add(param);
        }

        // Execute and map results...
        return new List<User>(); // Simplified for example
    }

    // Example with SQL Server connection-specific parameters
    public async Task<List<User>> GetUsersWithSqlServerConnection(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForSqlServerWithConnection(filters, connection);
        using var command = connection.CreateCommand();
        command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";

        foreach (var param in result.Parameters)
        {
            command.Parameters.Add(param);
        }

        // Execute and map results...
        return new List<User>(); // Simplified for example
    }
}

public class User
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Age { get; set; }
    public bool IsActive { get; set; }
}
