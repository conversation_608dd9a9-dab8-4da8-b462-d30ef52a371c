using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Extensions;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.SqlServer;
using Q.FilterBuilder.SqlServer.Extensions;
using System.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Dapper;

namespace Q.FilterBuilder.Examples;

/// <summary>
/// Example demonstrating the new SOLID-compliant adapter-specific extension methods.
/// This approach follows SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public class AdapterUsageExample
{
    private readonly IFilterBuilder _filterBuilder;

    public AdapterUsageExample()
    {
        // Simple setup - just inject IFilterBuilder
        _filterBuilder = new FilterBuilder(new SqlServerFormatProvider());
    }

    public void DemonstrateUsage()
    {
        // Create a filter group
        var filters = new FilterGroup("AND");
        filters.Rules.Add(new FilterRule("Name", "contains", "John"));
        filters.Rules.Add(new FilterRule("Age", "greater", 25));
        filters.Rules.Add(new FilterRule("IsActive", "equal", true));

        // 1. For Dapper - get query and parameter dictionary (using extension method)
        var dapperResult = _filterBuilder.BuildForDapper(filters);
        // Use with Dapper: connection.Query<User>($"SELECT * FROM Users WHERE {dapperResult.WhereClause}", dapperResult.Parameters);

        // 2. For EF Core - get formatted query with {0}, {1} placeholders (using extension method)
        var efCoreResult = _filterBuilder.BuildForEfCore(filters);
        // Use with EF Core: context.Users.FromSqlRaw($"SELECT * FROM Users WHERE {efCoreResult.FormattedQuery}", efCoreResult.Parameters);

        // 3. For EF6 - same format as EF Core (using extension method)
        var efResult = _filterBuilder.BuildForEf(filters);
        // Use with EF6: context.Users.SqlQuery($"SELECT * FROM Users WHERE {efResult.FormattedQuery}", efResult.Parameters);

        // 4. For ADO.NET - get database parameters (using extension method)
        var adoNetResult = _filterBuilder.BuildForAdoNet(filters);
        // Use with ADO.NET: command.CommandText = $"SELECT * FROM Users WHERE {adoNetResult.WhereClause}";
        // foreach(var param in adoNetResult.Parameters) command.Parameters.Add(param);

        // 5. For SQL Server ADO.NET - get optimized SQL Server parameters (provider-specific extension)
        var sqlServerResult = _filterBuilder.BuildForSqlServerAdoNet(filters);
        // Use with SQL Server: command.CommandText = $"SELECT * FROM Users WHERE {sqlServerResult.WhereClause}";
        // foreach(var param in sqlServerResult.Parameters) command.Parameters.Add(param);

        // 6. For Dynamic LINQ - get LINQ expression string (using extension method)
        var linqResult = _filterBuilder.BuildForDynamicLinq(filters);
        // Use with Dynamic LINQ: users.Where(linqResult.LinqExpression);

        // 7. Traditional way still works
        var (query, parameters) = _filterBuilder.Build(filters);
        // Use however you want: query contains WHERE clause, parameters contains values
    }

    // Example with Entity Framework Core
    public async Task<List<User>> GetUsersWithEfCore(DbContext context, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForEfCore(filters);
        var sql = $"SELECT * FROM Users WHERE {result.FormattedQuery}";
        return await context.Set<User>().FromSqlRaw(sql, result.Parameters).ToListAsync();
    }

    // Example with Dapper
    public async Task<List<User>> GetUsersWithDapper(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForDapper(filters);
        var sql = $"SELECT * FROM Users WHERE {result.WhereClause}";
        return (await connection.QueryAsync<User>(sql, result.Parameters)).ToList();
    }

    // Example with ADO.NET using default implementation
    public async Task<List<User>> GetUsersWithAdoNet(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForAdoNet(filters);
        using var command = connection.CreateCommand();
        command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";

        foreach (var param in result.Parameters)
        {
            command.Parameters.Add(param);
        }

        // Execute and map results...
        return new List<User>(); // Simplified for example
    }

    // Example with SQL Server optimized ADO.NET (provider-specific extension)
    public async Task<List<User>> GetUsersWithSqlServerAdoNet(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForSqlServerAdoNet(filters);
        using var command = connection.CreateCommand();
        command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";

        foreach (var param in result.Parameters)
        {
            command.Parameters.Add(param);
        }

        // Execute and map results...
        return new List<User>(); // Simplified for example
    }

    // Example with SQL Server connection-specific parameters
    public async Task<List<User>> GetUsersWithSqlServerConnection(SqlConnection connection, FilterGroup filters)
    {
        var result = _filterBuilder.BuildForSqlServerWithConnection(filters, connection);
        using var command = connection.CreateCommand();
        command.CommandText = $"SELECT * FROM Users WHERE {result.WhereClause}";

        foreach (var param in result.Parameters)
        {
            command.Parameters.Add(param);
        }

        // Execute and map results...
        return new List<User>(); // Simplified for example
    }
}

public class User
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Age { get; set; }
    public bool IsActive { get; set; }
}
