using System;
using Q.FilterBuilder.Core.Adapters;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;
using Q.FilterBuilder.Core.TypeConversion;
using Q.FilterBuilder.Core.RuleTransformers;

namespace Q.FilterBuilder.Core;

/// <summary>
/// Factory for creating adapter-specific filter builders.
/// </summary>
public class FilterBuilderFactory : IFilterBuilderFactory
{
    private readonly IQueryFormatProvider _queryFormatProvider;
    private readonly ITypeConversionService _typeConversionService;
    private readonly IRuleTransformerService _ruleTransformerService;

    /// <summary>
    /// Initializes a new instance of the FilterBuilderFactory class.
    /// </summary>
    /// <param name="queryFormatProvider">The query format provider</param>
    /// <param name="typeConversionService">The type conversion service</param>
    /// <param name="ruleTransformerService">The rule transformer service</param>
    public FilterBuilderFactory(
        IQueryFormatProvider queryFormatProvider,
        ITypeConversionService typeConversionService,
        IRuleTransformerService ruleTransformerService)
    {
        _queryFormatProvider = queryFormatProvider ?? throw new ArgumentNullException(nameof(queryFormatProvider));
        _typeConversionService = typeConversionService ?? throw new ArgumentNullException(nameof(typeConversionService));
        _ruleTransformerService = ruleTransformerService ?? throw new ArgumentNullException(nameof(ruleTransformerService));
    }

    /// <summary>
    /// Creates an EF Core filter builder.
    /// </summary>
    /// <returns>An EF Core filter builder instance.</returns>
    public IFilterBuilder<EfCoreQueryResult> CreateEfCoreBuilder()
    {
        return new EfCoreFilterBuilder(_queryFormatProvider, _typeConversionService, _ruleTransformerService);
    }

    /// <summary>
    /// Creates a Dapper filter builder.
    /// </summary>
    /// <returns>A Dapper filter builder instance.</returns>
    public IFilterBuilder<DapperQueryResult> CreateDapperBuilder()
    {
        return new DapperFilterBuilder(_queryFormatProvider, _typeConversionService, _ruleTransformerService);
    }

    /// <summary>
    /// Creates an ADO.NET filter builder.
    /// </summary>
    /// <returns>An ADO.NET filter builder instance.</returns>
    public IFilterBuilder<AdoNetQueryResult> CreateAdoNetBuilder()
    {
        return new AdoNetFilterBuilder(_queryFormatProvider, _typeConversionService, _ruleTransformerService);
    }

    /// <summary>
    /// Creates a Dynamic LINQ filter builder.
    /// </summary>
    /// <returns>A Dynamic LINQ filter builder instance.</returns>
    public IFilterBuilder<LinqQueryResult> CreateLinqBuilder()
    {
        return new LinqFilterBuilder(_queryFormatProvider, _typeConversionService, _ruleTransformerService);
    }

    /// <summary>
    /// Creates an EF6 filter builder.
    /// </summary>
    /// <returns>An EF6 filter builder instance.</returns>
    public IFilterBuilder<Ef6QueryResult> CreateEf6Builder()
    {
        return new Ef6FilterBuilder(_queryFormatProvider, _typeConversionService, _ruleTransformerService);
    }
}
