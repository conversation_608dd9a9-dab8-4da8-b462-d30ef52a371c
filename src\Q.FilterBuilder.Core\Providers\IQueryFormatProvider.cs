using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;

namespace Q.FilterBuilder.Core.Providers;

/// <summary>
/// Defines the interface for database-specific query syntax providers.
/// Each database (SqlServer, MySQL, Postgres, etc.) implements this interface to provide
/// database-specific formatting rules for parameters, field names, and logical operators.
/// </summary>
public interface IQueryFormatProvider
{
    /// <summary>
    /// Gets the parameter prefix for this database (e.g., "@" for SQL Server, "?" for MySQL).
    /// </summary>
    string ParameterPrefix { get; }

    /// <summary>
    /// Gets the logical operator representation for AND conditions.
    /// </summary>
    string AndOperator { get; }

    /// <summary>
    /// Gets the logical operator representation for OR conditions.
    /// </summary>
    string OrOperator { get; }

    /// <summary>
    /// Formats a field name according to database conventions (e.g., [FieldName] for SQL Server).
    /// </summary>
    /// <param name="fieldName">The field name to format.</param>
    /// <returns>The formatted field name.</returns>
    string FormatFieldName(string fieldName);

    /// <summary>
    /// Formats a parameter name according to database conventions.
    /// </summary>
    /// <param name="parameterIndex">The parameter index.</param>
    /// <returns>The formatted parameter name.</returns>
    string FormatParameterName(int parameterIndex);

    /// <summary>
    /// Converts a raw WHERE clause to EF Core format with {0}, {1} placeholders.
    /// Default implementation replaces parameter names with indexed placeholders.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <returns>The EF Core formatted query</returns>
    string ToEfCoreFormat(string whereClause)
    {
        var efQuery = whereClause;
        var parameterPattern = Regex.Escape(ParameterPrefix) + @"p(\d+)";
        efQuery = Regex.Replace(efQuery, parameterPattern, "{$1}");
        return efQuery;
    }

    /// <summary>
    /// Converts parameter values to Dapper parameter dictionary.
    /// Default implementation creates a dictionary with parameter names as keys.
    /// </summary>
    /// <param name="parameterValues">The parameter values</param>
    /// <returns>A dictionary suitable for Dapper</returns>
    Dictionary<string, object?> ToDapperParameters(object[] parameterValues)
    {
        var paramDict = new Dictionary<string, object?>();
        for (var i = 0; i < parameterValues.Length; i++)
        {
            paramDict[FormatParameterName(i)] = parameterValues[i];
        }
        return paramDict;
    }

    /// <summary>
    /// Creates ADO.NET database parameters from parameter values.
    /// Default implementation returns empty array - override in specific providers.
    /// </summary>
    /// <param name="parameterValues">The parameter values</param>
    /// <returns>An array of database parameters</returns>
    IDbDataParameter[] ToAdoNetParameters(object[] parameterValues)
    {
        // Default implementation returns empty array
        // Specific providers should override this method
        return new IDbDataParameter[0];
    }

    /// <summary>
    /// Converts a WHERE clause to Dynamic LINQ format.
    /// Default implementation performs basic SQL to LINQ conversion.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <returns>A Dynamic LINQ expression string</returns>
    string ToLinqExpression(string whereClause, object[] parameterValues)
    {
        var linqExpression = whereClause;

        // Replace SQL operators with LINQ equivalents
        linqExpression = linqExpression.Replace(" = ", " == ");
        linqExpression = linqExpression.Replace(" <> ", " != ");
        linqExpression = linqExpression.Replace(" AND ", " && ");
        linqExpression = linqExpression.Replace(" OR ", " || ");

        // Replace field brackets/quotes with property access
        linqExpression = Regex.Replace(linqExpression, @"[\[\]`""]", "");

        // Replace parameters with actual values (simplified)
        for (var i = 0; i < parameterValues.Length; i++)
        {
            var paramName = FormatParameterName(i);
            var value = parameterValues[i];
            var valueString = value switch
            {
                string s => $"\"{s}\"",
                null => "null",
                _ => value.ToString()
            };
            linqExpression = linqExpression.Replace(paramName, valueString);
        }

        return linqExpression;
    }
}
