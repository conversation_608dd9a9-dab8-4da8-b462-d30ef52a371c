using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;

namespace Q.FilterBuilder.Core.Providers;

/// <summary>
/// Defines the interface for database-specific query syntax providers.
/// Each database (SqlServer, MySQL, Postgres, etc.) implements this interface to provide
/// database-specific formatting rules for parameters, field names, and logical operators.
/// </summary>
public interface IQueryFormatProvider
{
    /// <summary>
    /// Gets the parameter prefix for this database (e.g., "@" for SQL Server, "?" for MySQL).
    /// </summary>
    string ParameterPrefix { get; }

    /// <summary>
    /// Gets the logical operator representation for AND conditions.
    /// </summary>
    string AndOperator { get; }

    /// <summary>
    /// Gets the logical operator representation for OR conditions.
    /// </summary>
    string OrOperator { get; }

    /// <summary>
    /// Formats a field name according to database conventions (e.g., [FieldName] for SQL Server).
    /// </summary>
    /// <param name="fieldName">The field name to format.</param>
    /// <returns>The formatted field name.</returns>
    string FormatFieldName(string fieldName);

    /// <summary>
    /// Formats a parameter name according to database conventions.
    /// </summary>
    /// <param name="parameterIndex">The parameter index.</param>
    /// <returns>The formatted parameter name.</returns>
    string FormatParameterName(int parameterIndex);

    /// <summary>
    /// Converts a raw WHERE clause to EF Core format with {0}, {1} placeholders.
    /// Default implementation replaces parameter names with indexed placeholders.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <returns>The EF Core formatted query</returns>
    string ToEfCoreFormat(string whereClause)
    {
        var efQuery = whereClause;
        var parameterPattern = Regex.Escape(ParameterPrefix) + @"p(\d+)";
        efQuery = Regex.Replace(efQuery, parameterPattern, "{$1}");
        return efQuery;
    }

    /// <summary>
    /// Converts parameter values to Dapper parameter dictionary.
    /// Default implementation creates a dictionary with parameter names as keys.
    /// </summary>
    /// <param name="parameterValues">The parameter values</param>
    /// <returns>A dictionary suitable for Dapper</returns>
    Dictionary<string, object?> ToDapperParameters(object[] parameterValues)
    {
        var paramDict = new Dictionary<string, object?>();
        for (var i = 0; i < parameterValues.Length; i++)
        {
            paramDict[FormatParameterName(i)] = parameterValues[i];
        }
        return paramDict;
    }

    /// <summary>
    /// Creates ADO.NET database parameters from parameter values.
    /// Default implementation returns empty array - providers should override for optimal performance.
    /// </summary>
    /// <param name="parameterValues">The parameter values</param>
    /// <returns>An array of database parameters</returns>
    IDbDataParameter[] ToAdoNetParameters(object[] parameterValues)
    {
        // Default implementation returns empty array
        // Use AdoNetExtensions.BuildForAdoNetWithConnection for automatic parameter creation
        // or override this method in specific providers for optimized parameter creation
        return new IDbDataParameter[0];
    }
}
