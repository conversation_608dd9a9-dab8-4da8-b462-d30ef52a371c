using System.Collections.Generic;
using System.Text.RegularExpressions;
using Q.FilterBuilder.Core.Models;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support Dynamic LINQ integration.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class DynamicLinqExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Dynamic LINQ.
    /// This method converts SQL syntax to Dynamic LINQ syntax while preserving parameters.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DynamicLinqQueryResult with LINQ expression and parameters.</returns>
    /// <example>
    /// <code>
    /// var result = filterBuilder.BuildForDynamicLinq(filterGroup);
    /// 
    /// // Option 1: Use with parameter array
    /// var users = queryableUsers.Where(result.LinqExpression, result.Parameters);
    /// 
    /// // Option 2: Use with parameter dictionary
    /// var users = queryableUsers.Where(result.LinqExpression, result.ParameterDictionary);
    /// </code>
    /// </example>
    public static DynamicLinqQueryResult BuildForDynamicLinq(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var (linqExpression, parameterDict) = ConvertToLinqExpression(query, parameters, filterBuilder.QueryFormatProvider);
        return new DynamicLinqQueryResult(query, linqExpression, parameters, parameterDict);
    }

    /// <summary>
    /// Converts a SQL WHERE clause to Dynamic LINQ format.
    /// This method properly handles parameter conversion for Dynamic LINQ syntax.
    /// </summary>
    /// <param name="whereClause">The SQL WHERE clause</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="provider">The query format provider</param>
    /// <returns>A tuple containing the LINQ expression and parameter dictionary</returns>
    private static (string linqExpression, Dictionary<string, object?> parameterDict) ConvertToLinqExpression(
        string whereClause, 
        object[] parameterValues, 
        Providers.IQueryFormatProvider provider)
    {
        var linqExpression = whereClause;
        var parameterDict = new Dictionary<string, object?>();

        // Replace SQL operators with LINQ equivalents
        linqExpression = linqExpression.Replace(" = ", " == ");
        linqExpression = linqExpression.Replace(" <> ", " != ");
        linqExpression = linqExpression.Replace(" AND ", " && ");
        linqExpression = linqExpression.Replace(" OR ", " || ");

        // Replace SQL LIKE with LINQ Contains/StartsWith/EndsWith
        linqExpression = ConvertLikeOperators(linqExpression);

        // Replace field brackets/quotes with property access
        linqExpression = Regex.Replace(linqExpression, @"[\[\]`""]", "");

        // Replace SQL parameters with Dynamic LINQ parameter syntax
        for (var i = 0; i < parameterValues.Length; i++)
        {
            var sqlParamName = provider.FormatParameterName(i);
            var linqParamName = $"@{i}";
            var dictParamName = $"param{i}";
            
            linqExpression = linqExpression.Replace(sqlParamName, linqParamName);
            parameterDict[dictParamName] = parameterValues[i];
        }

        return (linqExpression, parameterDict);
    }

    /// <summary>
    /// Converts SQL LIKE operators to appropriate LINQ string methods.
    /// </summary>
    /// <param name="expression">The expression to convert</param>
    /// <returns>The converted expression</returns>
    private static string ConvertLikeOperators(string expression)
    {
        // Convert LIKE '%value%' to Contains
        expression = Regex.Replace(expression, @"(\w+)\s+LIKE\s+@p(\d+)", 
            match => $"{match.Groups[1].Value}.Contains(@{match.Groups[2].Value})");

        // Convert NOT LIKE '%value%' to !Contains
        expression = Regex.Replace(expression, @"(\w+)\s+NOT\s+LIKE\s+@p(\d+)", 
            match => $"!{match.Groups[1].Value}.Contains(@{match.Groups[2].Value})");

        return expression;
    }

    /// <summary>
    /// Builds a query result for Dynamic LINQ with custom field mapping.
    /// This method allows you to map database field names to object property names.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <param name="fieldMapping">A dictionary mapping database field names to property names.</param>
    /// <returns>A DynamicLinqQueryResult with mapped property names.</returns>
    /// <example>
    /// <code>
    /// var fieldMapping = new Dictionary&lt;string, string&gt;
    /// {
    ///     { "user_name", "UserName" },
    ///     { "created_date", "CreatedDate" }
    /// };
    /// var result = filterBuilder.BuildForDynamicLinqWithMapping(filterGroup, fieldMapping);
    /// </code>
    /// </example>
    public static DynamicLinqQueryResult BuildForDynamicLinqWithMapping(
        this IFilterBuilder filterBuilder, 
        FilterGroup group, 
        Dictionary<string, string> fieldMapping)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var (linqExpression, parameterDict) = ConvertToLinqExpression(query, parameters, filterBuilder.QueryFormatProvider);
        
        // Apply field mapping
        foreach (var mapping in fieldMapping)
        {
            linqExpression = linqExpression.Replace(mapping.Key, mapping.Value);
        }
        
        return new DynamicLinqQueryResult(query, linqExpression, parameters, parameterDict);
    }
}
