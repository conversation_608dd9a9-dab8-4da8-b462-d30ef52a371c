using System;
using Xunit;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Extensions;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;
using Q.FilterBuilder.Core.TypeConversion;
using Q.FilterBuilder.Core.RuleTransformers;
using Q.FilterBuilder.SqlServer;

namespace Q.FilterBuilder.Core.Tests;

/// <summary>
/// Tests for the new simplified adapter architecture.
/// </summary>
public class AdapterArchitectureTests
{
    private readonly IFilterBuilder _filterBuilder;

    public AdapterArchitectureTests()
    {
        var sqlServerProvider = new SqlServerFormatProvider();
        var typeConversionService = new TypeConversionService();
        var ruleTransformerService = new RuleTransformerService();
        _filterBuilder = new FilterBuilder(sqlServerProvider, typeConversionService, ruleTransformerService);
    }

    [Fact]
    public void BuildForEfCore_ShouldCreateCorrectResult()
    {
        // Arrange
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));
        filterGroup.Rules.Add(new FilterRule("Age", "greater", 25));

        // Act
        var result = _filterBuilder.BuildForEfCore(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("[Age]", result.WhereClause);
        Assert.Contains("{0}", result.FormattedQuery);
        Assert.Contains("{1}", result.FormattedQuery);
        Assert.Equal(2, result.Parameters.Length);
        Assert.Equal("John", result.Parameters[0]);
        Assert.Equal(25, result.Parameters[1]);
    }

    [Fact]
    public void BuildForDapper_ShouldCreateCorrectResult()
    {
        // Arrange
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));
        filterGroup.Rules.Add(new FilterRule("Age", "greater", 25));

        // Act
        var result = _filterBuilder.BuildForDapper(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("[Age]", result.WhereClause);
        Assert.Equal(2, result.Parameters.Count);
        Assert.True(result.Parameters.ContainsKey("@p0"));
        Assert.True(result.Parameters.ContainsKey("@p1"));
        Assert.Equal("John", result.Parameters["@p0"]);
        Assert.Equal(25, result.Parameters["@p1"]);
    }

    [Fact]
    public void BuildForAdoNet_ShouldCreateCorrectResult()
    {
        // Arrange
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

        // Act
        var result = _filterBuilder.BuildForAdoNet(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("[Name]", result.WhereClause);
        // Note: Parameters array will be empty for now as we use default implementation
        Assert.Empty(result.Parameters);
    }

    [Fact]
    public void BuildForDynamicLinq_ShouldCreateCorrectResult()
    {
        // Arrange
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

        // Act
        var result = _filterBuilder.BuildForDynamicLinq(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("Name", result.LinqExpression);
        Assert.Contains("==", result.LinqExpression);
        Assert.Single(result.Parameters);
        Assert.Equal("John", result.Parameters[0]);
    }

    [Fact]
    public void BuildForEf_ShouldCreateCorrectResult()
    {
        // Arrange
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

        // Act
        var result = _filterBuilder.BuildForEf(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("{0}", result.FormattedQuery);
        Assert.Single(result.Parameters);
        Assert.Equal("John", result.Parameters[0]);
    }

    [Fact]
    public void Build_ShouldReturnBasicQueryAndParameters()
    {
        // Arrange
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));
        filterGroup.Rules.Add(new FilterRule("Age", "greater", 25));

        // Act
        var (query, parameters) = _filterBuilder.Build(filterGroup);

        // Assert
        Assert.NotNull(query);
        Assert.Contains("[Name]", query);
        Assert.Contains("[Age]", query);
        Assert.Equal(2, parameters.Length);
        Assert.Equal("John", parameters[0]);
        Assert.Equal(25, parameters[1]);
    }

    [Fact]
    public void AllAdapterMethods_ShouldWorkWithSameFilterGroup()
    {
        // Arrange
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

        // Act & Assert - All methods should work without throwing exceptions
        var basicResult = _filterBuilder.Build(filterGroup);
        var dapperResult = _filterBuilder.BuildForDapper(filterGroup);
        var efCoreResult = _filterBuilder.BuildForEfCore(filterGroup);
        var efResult = _filterBuilder.BuildForEf(filterGroup);
        var adoNetResult = _filterBuilder.BuildForAdoNet(filterGroup);
        var linqResult = _filterBuilder.BuildForDynamicLinq(filterGroup);

        // All should have the same base WHERE clause
        Assert.Equal(basicResult.parsedQuery, dapperResult.WhereClause);
        Assert.Equal(basicResult.parsedQuery, efCoreResult.WhereClause);
        Assert.Equal(basicResult.parsedQuery, efResult.WhereClause);
        Assert.Equal(basicResult.parsedQuery, adoNetResult.WhereClause);
        Assert.Equal(basicResult.parsedQuery, linqResult.WhereClause);
    }
}
