using System;
using Xunit;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Adapters;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;
using Q.FilterBuilder.Core.TypeConversion;
using Q.FilterBuilder.Core.RuleTransformers;
using Q.FilterBuilder.SqlServer;

namespace Q.FilterBuilder.Core.Tests;

/// <summary>
/// Tests for the new adapter-first architecture.
/// </summary>
public class AdapterArchitectureTests
{
    private readonly IQueryFormatProvider _sqlServerProvider;
    private readonly ITypeConversionService _typeConversionService;
    private readonly IRuleTransformerService _ruleTransformerService;
    private readonly IFilterBuilderFactory _factory;

    public AdapterArchitectureTests()
    {
        _sqlServerProvider = new SqlServerFormatProvider();
        _typeConversionService = new TypeConversionService();
        _ruleTransformerService = new RuleTransformerService();
        _factory = new FilterBuilderFactory(_sqlServerProvider, _typeConversionService, _ruleTransformerService);
    }

    [Fact]
    public void EfCoreFilterBuilder_ShouldCreateCorrectResult()
    {
        // Arrange
        var efCoreBuilder = _factory.CreateEfCoreBuilder();
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));
        filterGroup.Rules.Add(new FilterRule("Age", "greater", 25));

        // Act
        var result = efCoreBuilder.Build(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<EfCoreQueryResult>(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("[Age]", result.WhereClause);
        Assert.Contains("{0}", result.FormattedQuery);
        Assert.Contains("{1}", result.FormattedQuery);
        Assert.Equal(2, result.ParameterValues.Length);
        Assert.Equal("John", result.ParameterValues[0]);
        Assert.Equal(25, result.ParameterValues[1]);
    }

    [Fact]
    public void DapperFilterBuilder_ShouldCreateCorrectResult()
    {
        // Arrange
        var dapperBuilder = _factory.CreateDapperBuilder();
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));
        filterGroup.Rules.Add(new FilterRule("Age", "greater", 25));

        // Act
        var result = dapperBuilder.Build(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<DapperQueryResult>(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("[Age]", result.WhereClause);
        Assert.Equal(2, result.Parameters.Count);
        Assert.True(result.Parameters.ContainsKey("@p0"));
        Assert.True(result.Parameters.ContainsKey("@p1"));
        Assert.Equal("John", result.Parameters["@p0"]);
        Assert.Equal(25, result.Parameters["@p1"]);
    }

    [Fact]
    public void AdoNetFilterBuilder_ShouldCreateCorrectResult()
    {
        // Arrange
        var adoNetBuilder = _factory.CreateAdoNetBuilder();
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

        // Act
        var result = adoNetBuilder.Build(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<AdoNetQueryResult>(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Equal(1, result.ParameterValues.Length);
        Assert.Equal("John", result.ParameterValues[0]);
        // Note: DbParameters array will be empty for now as we simplified the implementation
    }

    [Fact]
    public void LinqFilterBuilder_ShouldCreateCorrectResult()
    {
        // Arrange
        var linqBuilder = _factory.CreateLinqBuilder();
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

        // Act
        var result = linqBuilder.Build(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<LinqQueryResult>(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("Name", result.LinqExpression);
        Assert.Contains("==", result.LinqExpression);
        Assert.Equal(1, result.ParameterValues.Length);
        Assert.Equal("John", result.ParameterValues[0]);
    }

    [Fact]
    public void Ef6FilterBuilder_ShouldCreateCorrectResult()
    {
        // Arrange
        var ef6Builder = _factory.CreateEf6Builder();
        var filterGroup = new FilterGroup("AND");
        filterGroup.Rules.Add(new FilterRule("Name", "equal", "John"));

        // Act
        var result = ef6Builder.Build(filterGroup);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<Ef6QueryResult>(result);
        Assert.Contains("[Name]", result.WhereClause);
        Assert.Contains("{0}", result.FormattedQuery);
        Assert.Equal(1, result.ParameterValues.Length);
        Assert.Equal("John", result.ParameterValues[0]);
    }

    [Fact]
    public void BuildContext_ShouldSupportAdapterFeatures()
    {
        // Arrange & Act
        var efCoreContext = new BuildContext(AdapterType.EfCore, _sqlServerProvider);
        var dapperContext = new BuildContext(AdapterType.Dapper, _sqlServerProvider);
        var linqContext = new BuildContext(AdapterType.Linq, _sqlServerProvider);

        // Assert
        Assert.True(efCoreContext.SupportsFeature("raw_sql"));
        Assert.True(efCoreContext.SupportsFeature("formatted_queries"));
        Assert.False(efCoreContext.SupportsFeature("expressions"));

        Assert.True(dapperContext.SupportsFeature("raw_sql"));
        Assert.True(dapperContext.SupportsFeature("dynamic_parameters"));
        Assert.False(dapperContext.SupportsFeature("expressions"));

        Assert.True(linqContext.SupportsFeature("expressions"));
        Assert.False(linqContext.SupportsFeature("raw_sql"));
    }

    [Fact]
    public void BuildContext_ShouldManageParametersCorrectly()
    {
        // Arrange
        var context = new BuildContext(AdapterType.EfCore, _sqlServerProvider);

        // Act
        var param1 = context.AddParameter("John");
        var param2 = context.AddParameter(25);
        var param3 = context.AddParameter(null);

        // Assert
        Assert.Equal("@p0", param1);
        Assert.Equal("@p1", param2);
        Assert.Equal("@p2", param3);
        Assert.Equal(3, context.Parameters.Count);
        Assert.Equal("John", context.Parameters[0]);
        Assert.Equal(25, context.Parameters[1]);
        Assert.Equal(System.DBNull.Value, context.Parameters[2]);
    }
}
