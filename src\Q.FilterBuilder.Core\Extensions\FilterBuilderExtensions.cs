using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;

namespace Q.FilterBuilder.Core.Extensions;

/// <summary>
/// Extension methods for IFilterBuilder to support different ORM adapters.
/// These extensions follow SOLID principles by not requiring modification of the core FilterBuilder class.
/// </summary>
public static class FilterBuilderExtensions
{
    /// <summary>
    /// Builds a query result specifically formatted for Dapper.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DapperQueryResult with query and parameter dictionary.</returns>
    public static DapperQueryResult BuildForDapper(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var dapperParameters = filterBuilder.QueryFormatProvider.ToDapperParameters(parameters);
        return new DapperQueryResult(query, dapperParameters);
    }

    /// <summary>
    /// Builds a query result specifically formatted for Entity Framework Core.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An EfCoreQueryResult with formatted query and parameters.</returns>
    public static EfCoreQueryResult BuildForEfCore(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var formattedQuery = filterBuilder.QueryFormatProvider.ToEfCoreFormat(query);
        return new EfCoreQueryResult(query, formattedQuery, parameters);
    }

    /// <summary>
    /// Builds a query result specifically formatted for Entity Framework 6.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An EfQueryResult with formatted query and parameters.</returns>
    public static EfQueryResult BuildForEf(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var formattedQuery = filterBuilder.QueryFormatProvider.ToEfCoreFormat(query); // EF6 uses same format as EF Core
        return new EfQueryResult(query, formattedQuery, parameters);
    }

    /// <summary>
    /// Builds a query result specifically formatted for ADO.NET.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>An AdoNetQueryResult with database parameters.</returns>
    public static AdoNetQueryResult BuildForAdoNet(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var dbParameters = filterBuilder.QueryFormatProvider.ToAdoNetParameters(parameters);
        return new AdoNetQueryResult(query, dbParameters);
    }

    /// <summary>
    /// Builds a query result specifically formatted for Dynamic LINQ.
    /// </summary>
    /// <param name="filterBuilder">The filter builder instance.</param>
    /// <param name="group">The FilterGroup to build the query from.</param>
    /// <returns>A DynamicLinqQueryResult with LINQ expression and parameters.</returns>
    public static DynamicLinqQueryResult BuildForDynamicLinq(this IFilterBuilder filterBuilder, FilterGroup group)
    {
        var (query, parameters) = filterBuilder.Build(group);
        var linqExpression = filterBuilder.QueryFormatProvider.ToLinqExpression(query, parameters);
        return new DynamicLinqQueryResult(query, linqExpression, parameters);
    }
}
