using System;
using System.Text;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.Core.Providers;
using Q.FilterBuilder.Core.TypeConversion;
using Q.FilterBuilder.Core.RuleTransformers;

namespace Q.FilterBuilder.Core.Adapters;

/// <summary>
/// Dynamic LINQ specific filter builder implementation.
/// </summary>
public class LinqFilterBuilder : IFilterBuilder<LinqQueryResult>
{
    private readonly IQueryFormatProvider _queryFormatProvider;
    private readonly ITypeConversionService _typeConversionService;
    private readonly IRuleTransformerService _ruleTransformerService;

    /// <summary>
    /// Initializes a new instance of the LinqFilterBuilder class.
    /// </summary>
    /// <param name="queryFormatProvider">The query format provider</param>
    /// <param name="typeConversionService">The type conversion service</param>
    /// <param name="ruleTransformerService">The rule transformer service</param>
    public LinqFilterBuilder(
        IQueryFormatProvider queryFormatProvider,
        ITypeConversionService typeConversionService,
        IRuleTransformerService ruleTransformerService)
    {
        _queryFormatProvider = queryFormatProvider ?? throw new ArgumentNullException(nameof(queryFormatProvider));
        _typeConversionService = typeConversionService ?? throw new ArgumentNullException(nameof(typeConversionService));
        _ruleTransformerService = ruleTransformerService ?? throw new ArgumentNullException(nameof(ruleTransformerService));
    }

    /// <summary>
    /// Builds a Dynamic LINQ specific query result from a FilterGroup.
    /// </summary>
    /// <param name="group">The FilterGroup to build the query from</param>
    /// <returns>A LinqQueryResult containing the LINQ expression and parameters</returns>
    public LinqQueryResult Build(FilterGroup group)
    {
        if (group == null)
            throw new ArgumentNullException(nameof(group));

        var context = new BuildContext(AdapterType.Linq, _queryFormatProvider);
        var whereClause = BuildGroup(group, context);
        var parameterValues = context.Parameters.ToArray();
        var linqExpression = _queryFormatProvider.ToLinqExpression(whereClause, parameterValues);

        return new LinqQueryResult(whereClause, parameterValues, linqExpression);
    }

    /// <summary>
    /// Recursively builds a query from a FilterGroup.
    /// </summary>
    private string BuildGroup(FilterGroup group, BuildContext context)
    {
        var queryBuilder = new StringBuilder();
        var isFirst = true;

        // Process all rules in this group
        foreach (var rule in group.Rules)
        {
            if (!isFirst)
            {
                AppendLogicalOperator(queryBuilder, group.Condition, context);
            }

            var ruleQuery = BuildRule(rule, context);
            queryBuilder.Append(ruleQuery);
            isFirst = false;
        }

        // Process all sub-groups recursively
        foreach (var subGroup in group.Groups)
        {
            if (!isFirst)
            {
                AppendLogicalOperator(queryBuilder, group.Condition, context);
            }

            queryBuilder.Append("(");
            var subQuery = BuildGroup(subGroup, context); // Recursive call
            queryBuilder.Append(subQuery);
            queryBuilder.Append(")");
            isFirst = false;
        }

        return queryBuilder.ToString();
    }

    /// <summary>
    /// Builds a query condition from a FilterRule.
    /// </summary>
    private string BuildRule(FilterRule rule, BuildContext context)
    {
        // Convert rule value based on rule.Type before rule transformation
        rule.Value = _typeConversionService.ConvertValue(rule.Value, rule.Type, rule.Metadata);

        // Get rule transformer instance
        var ruleTransformer = _ruleTransformerService.GetRuleTransformer(rule.Operator);
        var fieldName = _queryFormatProvider.FormatFieldName(rule.FieldName);
        var parameterName = context.CreateParameterName();

        // Transform the rule using the rule transformer
        var (query, parameters) = ruleTransformer.Transform(rule, fieldName, parameterName);

        // Update context with parameters
        if (parameters != null)
        {
            context.Parameters.AddRange(parameters);
            context.ParameterIndex += parameters.Length;
        }

        return query;
    }

    /// <summary>
    /// Appends the appropriate logical operator (AND/OR) to the query.
    /// </summary>
    private void AppendLogicalOperator(StringBuilder queryBuilder, string condition, BuildContext context)
    {
        var logicalOperator = condition.ToUpper() switch
        {
            "AND" => context.QueryFormatProvider.AndOperator,
            "OR" => context.QueryFormatProvider.OrOperator,
            _ => condition
        };

        queryBuilder.Append($" {logicalOperator} ");
    }
}
