using System.Collections.Generic;
using System.Data;
using System.Linq.Expressions;

namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Query result specifically formatted for Entity Framework Core.
/// </summary>
public class EfCoreQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the query formatted for EF Core FromSqlRaw with {0}, {1} placeholders.
    /// </summary>
    public string FormattedQuery { get; }

    /// <summary>
    /// Gets the parameter values.
    /// </summary>
    public object[] Parameters { get; }

    /// <summary>
    /// Creates a new EF Core query result.
    /// </summary>
    public EfCoreQueryResult(string whereClause, string formattedQuery, object[] parameters)
    {
        WhereClause = whereClause;
        FormattedQuery = formattedQuery;
        Parameters = parameters;
    }
}

/// <summary>
/// Query result specifically formatted for Dapper.
/// </summary>
public class DapperQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the parameters formatted as a dictionary for Dapper.
    /// </summary>
    public Dictionary<string, object?> Parameters { get; }

    /// <summary>
    /// Creates a new Dapper query result.
    /// </summary>
    public DapperQueryResult(string whereClause, Dictionary<string, object?> parameters)
    {
        WhereClause = whereClause;
        Parameters = parameters;
    }
}

/// <summary>
/// Query result specifically formatted for ADO.NET.
/// </summary>
public class AdoNetQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the database parameters ready for ADO.NET commands.
    /// </summary>
    public IDbDataParameter[] Parameters { get; }

    /// <summary>
    /// Creates a new ADO.NET query result.
    /// </summary>
    public AdoNetQueryResult(string whereClause, IDbDataParameter[] parameters)
    {
        WhereClause = whereClause;
        Parameters = parameters;
    }
}

/// <summary>
/// Query result specifically formatted for Dynamic LINQ.
/// </summary>
public class DynamicLinqQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the Dynamic LINQ string expression.
    /// </summary>
    public string LinqExpression { get; }

    /// <summary>
    /// Gets the parameter values.
    /// </summary>
    public object[] Parameters { get; }

    /// <summary>
    /// Creates a new Dynamic LINQ query result.
    /// </summary>
    public DynamicLinqQueryResult(string whereClause, string linqExpression, object[] parameters)
    {
        WhereClause = whereClause;
        LinqExpression = linqExpression;
        Parameters = parameters;
    }
}

/// <summary>
/// Query result specifically formatted for Entity Framework 6.
/// </summary>
public class EfQueryResult
{
    /// <summary>
    /// Gets the raw WHERE clause.
    /// </summary>
    public string WhereClause { get; }

    /// <summary>
    /// Gets the query formatted for EF6 SqlQuery with {0}, {1} placeholders.
    /// </summary>
    public string FormattedQuery { get; }

    /// <summary>
    /// Gets the parameter values.
    /// </summary>
    public object[] Parameters { get; }

    /// <summary>
    /// Creates a new EF6 query result.
    /// </summary>
    public EfQueryResult(string whereClause, string formattedQuery, object[] parameters)
    {
        WhereClause = whereClause;
        FormattedQuery = formattedQuery;
        Parameters = parameters;
    }
}
