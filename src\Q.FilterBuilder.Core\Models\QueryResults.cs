using System.Collections.Generic;
using System.Data;
using System.Linq.Expressions;

namespace Q.FilterBuilder.Core.Models;

/// <summary>
/// Base class for all query results containing common properties.
/// </summary>
public abstract class QueryResultBase
{
    /// <summary>
    /// Gets the raw WHERE clause without SELECT statement.
    /// </summary>
    public string WhereClause { get; protected set; } = string.Empty;

    /// <summary>
    /// Gets the parameter values in their original format.
    /// </summary>
    public object[] ParameterValues { get; protected set; } = [];
}

/// <summary>
/// Query result specifically formatted for Entity Framework Core.
/// </summary>
public class EfCoreQueryResult : QueryResultBase
{
    /// <summary>
    /// Gets the query formatted for EF Core FromSqlRaw with {0}, {1} placeholders.
    /// </summary>
    public string FormattedQuery { get; private set; } = string.Empty;

    /// <summary>
    /// Creates a new EF Core query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="formattedQuery">The EF Core formatted query</param>
    public EfCoreQueryResult(string whereClause, object[] parameterValues, string formattedQuery)
    {
        WhereClause = whereClause;
        ParameterValues = parameterValues;
        FormattedQuery = formattedQuery;
    }
}

/// <summary>
/// Query result specifically formatted for Dapper.
/// </summary>
public class DapperQueryResult : QueryResultBase
{
    /// <summary>
    /// Gets the parameters formatted as a dictionary for Dapper.
    /// </summary>
    public Dictionary<string, object?> Parameters { get; private set; } = new();

    /// <summary>
    /// Creates a new Dapper query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="parameters">The Dapper parameter dictionary</param>
    public DapperQueryResult(string whereClause, object[] parameterValues, Dictionary<string, object?> parameters)
    {
        WhereClause = whereClause;
        ParameterValues = parameterValues;
        Parameters = parameters;
    }
}

/// <summary>
/// Query result specifically formatted for ADO.NET.
/// </summary>
public class AdoNetQueryResult : QueryResultBase
{
    /// <summary>
    /// Gets the database parameters ready for ADO.NET commands.
    /// </summary>
    public IDbDataParameter[] DbParameters { get; private set; } = [];

    /// <summary>
    /// Creates a new ADO.NET query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="dbParameters">The ADO.NET database parameters</param>
    public AdoNetQueryResult(string whereClause, object[] parameterValues, IDbDataParameter[] dbParameters)
    {
        WhereClause = whereClause;
        ParameterValues = parameterValues;
        DbParameters = dbParameters;
    }
}

/// <summary>
/// Query result specifically formatted for Dynamic LINQ.
/// </summary>
public class LinqQueryResult : QueryResultBase
{
    /// <summary>
    /// Gets the LINQ expression tree for the query.
    /// </summary>
    public Expression? Expression { get; private set; }

    /// <summary>
    /// Gets the Dynamic LINQ string expression.
    /// </summary>
    public string LinqExpression { get; private set; } = string.Empty;

    /// <summary>
    /// Creates a new LINQ query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="linqExpression">The Dynamic LINQ string expression</param>
    /// <param name="expression">The compiled LINQ expression tree</param>
    public LinqQueryResult(string whereClause, object[] parameterValues, string linqExpression, Expression? expression = null)
    {
        WhereClause = whereClause;
        ParameterValues = parameterValues;
        LinqExpression = linqExpression;
        Expression = expression;
    }
}

/// <summary>
/// Query result specifically formatted for Entity Framework 6.
/// </summary>
public class Ef6QueryResult : QueryResultBase
{
    /// <summary>
    /// Gets the query formatted for EF6 SqlQuery with {0}, {1} placeholders.
    /// </summary>
    public string FormattedQuery { get; private set; } = string.Empty;

    /// <summary>
    /// Creates a new EF6 query result.
    /// </summary>
    /// <param name="whereClause">The raw WHERE clause</param>
    /// <param name="parameterValues">The parameter values</param>
    /// <param name="formattedQuery">The EF6 formatted query</param>
    public Ef6QueryResult(string whereClause, object[] parameterValues, string formattedQuery)
    {
        WhereClause = whereClause;
        ParameterValues = parameterValues;
        FormattedQuery = formattedQuery;
    }
}
